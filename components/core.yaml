esphome:
  name: assistant-box
  friendly_name: 'Assistant Box'
  name_add_mac_suffix: true
  platformio_options:
    board_build.flash_mode: dio

esp32:
  board: esp32-s3-devkitc-1
  variant: esp32s3
  cpu_frequency: 240MHz
  flash_size: 16MB
  framework:
    type: esp-idf
    version: recommended
    sdkconfig_options:
      CONFIG_ESP32S3_DATA_CACHE_64KB: 'y'
      CONFIG_ESP32S3_DATA_CACHE_LINE_64B: 'y'
      CONFIG_ESP32S3_INSTRUCTION_CACHE_32KB: 'y'

      CONFIG_SPIRAM_RODATA: 'y'
      CONFIG_SPIRAM_FETCH_INSTRUCTIONS: 'y'

      CONFIG_BT_ALLOCATION_FROM_SPIRAM_FIRST: 'y'
      CONFIG_BT_BLE_DYNAMIC_ENV_MEMORY: 'y'

      CONFIG_MBEDTLS_EXTERNAL_MEM_ALLOC: 'y'
      CONFIG_MBEDTLS_SSL_PROTO_TLS1_3: 'y'

psram:
  mode: octal
  speed: 80MHz

i2c:
  sda: GPIO8
  scl: GPIO9
  scan: true
  frequency: 400kHz

button:
  - platform: restart
    id: reboot_button
    name: 'ESP Reboot'
    entity_category: diagnostic
    icon: 'mdi:restart'
    disabled_by_default: true

  - platform: factory_reset
    id: factory_reset_button
    name: 'ESP Factory Reset'
    entity_category: diagnostic
    icon: 'mdi:factory'
    disabled_by_default: true
