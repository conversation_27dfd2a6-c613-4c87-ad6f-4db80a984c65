event:
  - platform: template
    id: button_press_event
    name: 'Button Press'
    icon: mdi:button-pointer
    device_class: button
    event_types:
      - double_press
      - triple_press
      - long_press

binary_sensor:
  - platform: gpio
    id: center_button
    internal: true
    pin:
      number: GPIO47
      mode: INPUT_PULLUP
      inverted: true
    on_press:
      - script.execute: control_leds
    on_release:
      - script.execute: control_leds
      - if:
          condition:
            lambda: return id(factory_reset_requested);
          then:
            - button.press: factory_reset_button
    on_multi_click:
      # Simple Click:
      #   - Abort "things" in order
      #     - Timer
      #     - Announcements
      #     - Voice Assistant Pipeline run
      #     - Music
      #   - Starts the voice assistant if it is not yet running and if the device is not muted.
      - timing:
          - ON for at most 1s
          - OFF for at least 0.25s
        then:
          - if:
              condition:
                lambda: return !id(init_in_progress) && !id(color_changed);
              then:
                - if:
                    condition:
                      switch.is_on: timer_ringing
                    then:
                      - switch.turn_off: timer_ringing
                    else:
                      - if:
                          condition:
                            voice_assistant.is_running:
                          then:
                            - voice_assistant.stop:
                          else:
                            - if:
                                condition:
                                  media_player.is_announcing:
                                then:
                                  media_player.stop:
                                    announcement: true
                                else:
                                  - if:
                                      condition:
                                        media_player.is_playing:
                                      then:
                                        - media_player.pause:
                                      else:
                                        - if:
                                            condition:
                                              - not: voice_assistant.is_running
                                            then:
                                              - script.execute:
                                                  id: play_sound
                                                  priority: true
                                                  sound_file: !lambda return id(center_button_press_sound);
                                              - delay: 300ms
                                              - voice_assistant.start:

      # Double Click
      #  . Exposed as an event entity. To be used in automations inside Home Assistant
      - timing:
          - ON for at most 1s
          - OFF for at most 0.25s
          - ON for at most 1s
          - OFF for at least 0.25s
        then:
          - if:
              condition:
                lambda: return !id(init_in_progress) && !id(color_changed);
              then:
                - script.execute:
                    id: play_sound
                    priority: false
                    sound_file: !lambda return id(center_button_double_press_sound);
                - event.trigger:
                    id: button_press_event
                    event_type: 'double_press'

      # Triple Click
      #  . Exposed as an event entity. To be used in automations inside Home Assistant
      - timing:
          - ON for at most 1s
          - OFF for at most 0.25s
          - ON for at most 1s
          - OFF for at most 0.25s
          - ON for at most 1s
          - OFF for at least 0.25s
        then:
          - if:
              condition:
                lambda: return !id(init_in_progress) && !id(color_changed);
              then:
                - script.execute:
                    id: play_sound
                    priority: false
                    sound_file: !lambda return id(center_button_triple_press_sound);
                - event.trigger:
                    id: button_press_event
                    event_type: 'triple_press'

      # Long Press
      #  . Exposed as an event entity. To be used in automations inside Home Assistant
      - timing:
          - ON for at least 1s
        then:
          - if:
              condition:
                lambda: return !id(init_in_progress) && !id(color_changed);
              then:
                - script.execute:
                    id: play_sound
                    priority: false
                    sound_file: !lambda return id(center_button_long_press_sound);
                - light.turn_off: voice_assistant_leds
                - event.trigger:
                    id: button_press_event
                    event_type: 'long_press'

      # Factory Reset Warning
      #  . Audible and Visible warning.
      - timing:
          - ON for at least 10s
        then:
          - light.turn_on:
              brightness: 100%
              id: voice_assistant_leds
              effect: 'Factory Reset Coming Up'
          - script.execute:
              id: play_sound
              priority: true
              sound_file: !lambda return id(factory_reset_initiated_sound);
          - wait_until:
              binary_sensor.is_off: center_button
          - if:
              condition:
                lambda: return !id(factory_reset_requested);
              then:
                - light.turn_off: voice_assistant_leds
                - script.execute:
                    id: play_sound
                    priority: true
                    sound_file: !lambda return id(factory_reset_cancelled_sound);

      # Factory Reset Confirmed.
      #  . Audible warning to prompt user to release the button
      #  . Set factory_reset_requested to true
      - timing:
          - ON for at least 22s
        then:
          - script.execute:
              id: play_sound
              priority: true
              sound_file: !lambda return id(factory_reset_confirmed_sound);
          - light.turn_on:
              brightness: 100%
              red: 100%
              green: 0%
              blue: 0%
              id: voice_assistant_leds
              effect: 'none'
          - lambda: id(factory_reset_requested) = true;
